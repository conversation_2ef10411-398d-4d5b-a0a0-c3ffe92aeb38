import os
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 🔷 Шлях до папки з Word-файлами
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"

# 🔄 Список замін
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and  educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна (основні) галузь (галузі) знань за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': '',
    'Name and status of institution (if different from 2.3)': '',
    'administering studies': '',
    'Зазначено у пункті 2.3': '',
    'Specified in 2.3': '',
    '2.5': '2.4',
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ': 'ІНФОРМАЦІЯ ПРО КВАЛІФІКАЦІЮ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках': 'Офіційна тривалість освітньо-професійної програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years': 'Official length of educational-professional programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ': 'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED': 'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності)':'Найменування всіх закладів фахової передвищої освіти (структурних підрозділів або філій закладів фахової передвищої освіти), у яких здобувалася освітня кваліфікація (у тому числі заклади освіти, в яких здобувач фахової передвищої освіти вивчав окремі дисципліни за програмами академічної мобільності)',
    'Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of '
    'academic mobility': 'Names of all professional pre-higher education institutions (professional pre-higher education institutions separate structural units or branches) the qualification was gained in (including education institutions where the student of professional pre-higher education  studied separate course units within the framework of academic mobility programme)',
    'Контактна інформація закладу вищої освіти (наукової установи)': 'Контактна інформація закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Contact information of the higher education (research) institution': 'Contact information of the professional pre-higher education institution (other educational entity)',
    'Керівник або уповноважена особа закладу вищої освіти': 'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Capacity': 'Head or other authorized person of professional pre-higher education institution ',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)': 'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution': 'Position of the professional pre-higher education  institution head or other authorized person (other educational entity) ',
    'Печатка': 'Офіційна печатка',
    'Official stamp or seal': 'Official Seal',
    'НАЦІОНАЛЬНУ СИСТЕМУ ВИЩОЇ ОСВІТИ': 'СИСТЕМУ ФАХОВОЇ ПЕРЕДВИЩОЇ ОСВІТИ',
    'THE NATIONAL HIGHER EDUCATION SYSTEM': 'THE SYSTEM OF PROFESSIONAL PRE-HIGHER EDUCATION',
    'Інформація про національну систему вищої освіти, подана на наступних сторінках, висвітлює особливості кваліфікації вищої освіти та закладу вищої освіти, який її присвоїв': 'Інформація про систему фахової передвищої освіти, подана на наступних сторінках, висвітлює особливості освітньої кваліфікації фахової передвищої освіти та закладу фахової передвищої освіти, який її присвоїв',
    'The information on the national higher education system on the following pages provides a context for the qualification and the type of the awarding higher education institution': 'The information on the system of professional pre-higher education, provided on the following pages, highlights the features of the educational qualification of professional pre-higher education and awarding institution of professional pre-higher education',
    'Типи закладів вищої освіти ': 'Типи закладів фахової передвищої освіти',
    'Types of higher education institutions and their status': 'Types and status of professional pre-higher education institutions',
    'Підготовка в системі вищої освіти України здійснюється у таких закладах вищої': 'Підготовка в системі фахової передвищої освіти України здійснюється у таких закладах фахової передвищої',
    'Higher education studies in Ukraine are offered by the following higher education institutions': 'Training in the system of professional pre-higher education of Ukraine is carried out in the following professional pre-higher education institutions',
    'університет – багатогалузевий або галузевий заклад вищої освіти, що здійснює підготовку фахівців з вищою освітою за різними ступенями вищої освіти (у тому числі доктора філософії), проводить фундаментальні та / або прикладні наукові дослідження': 'тест2',
    'a university is a multisectoral or sectoral higher education institution that carries out educational activities for various degrees of higher education (including PhD), conducts fundamental and / or applied research': 'тест',
    'академії та інститути – галузеві заклади вищої освіти, що здійснюють підготовку фахівців з вищою освітою на першому (бакалаврському) і другому (магістерському) рівнях вищої освіти за однією чи кількома галузями знань та на третьому і вищому науковому рівнях вищої освіти за певними спеціальностями, проводять фундаментальні та / або прикладні наукові дослідження': 'тест',
    'academies and institutes are sectoral higher education institutions that carry out educational activities at the first (Bachelor) and the second (Master) levels of higher education in one or several Fields of Study, as well as at the third and higher scientific levels of higher education for certain Programme Subject Areas, and conduct fundamental and / or applied research': 'тест2',
    'коледж – заклад вищої освіти або структурний підрозділ університету, академії чи інституту, що здійснює підготовку фахівців з вищою освітою за ступенями вищої освіти бакалавра та/або молодшого бакалавра, проводить прикладні наукові дослідження та/або творчу мистецьку діяльність. Статус коледжу отримує заклад освіти (структурний підрозділ закладу освіти), в якому обсяг підготовки здобувачів вищої освіти ступеня бакалавра та / або молодшого бакалавра становить не менше 30 відсотків загального ліцензованого обсягу коледжу': 'тест',
    'a college is a higher education institution or structural unit of'
    'a university, academy or institute that carries out educational activities for a Bachelor\'s Professional pre-higher education educational-professional degree and/or a Junior Bachelor\'s Professional pre-higher education educational-professional degree, conducts applied research and/or creative art activity. The status of a college is granted to an educational institution (structural unit of an educational institution) in which provision of higher education for a Bachelor\'s Professional pre-higher education educational-professional degree and / or a Junior Bachelor\'s Professional pre-higher education educational-professional degree accounts for not less than 30 percent of the total licensed volume of a college': 'тест',
    'Статус національного закладу вищої освіти є почесним, надається за визначний внесок у розвиток вищої освіти, науки та культури України та відображається в найменуванні закладу вищої освіти': '',
    'The status of a national higher education institution is honorary, is awarded for the significant contribution to the development of higher education, science and culture of Ukraine, and is reflected in the official name of a higher education institution': '',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
    'test': 'тест',
    'Test': 'тест',
    'test2': 'тест2',
}

# Список ключів, для яких потрібно додати абзац і вирівнювання по ширині
keys_to_format = [
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності) /Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) ofacademic mobility)',
    'Контактна інформація закладу вищої освіти (наукової установи)',
    'Contact information of the higher education (research) institution',
    'Керівник або уповноважена особа закладу вищої освіти',
    'Capacity',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution',
    'Печатка',
    'Official stamp or seal',
]

def replace_text_in_paragraph(paragraph, doc):
    """Обробляє параграф: застосовує заміни, видаляє за потреби, додає абзац і вирівнювання."""
    full_text = ''.join(run.text for run in paragraph.runs)
    changed = False
    needs_formatting = False

    # Перевірка на заміну або видалення
    for old, new in replacements.items():
        if old in full_text:
            if new == '':  # Якщо значення порожнє, видаляємо параграф
                paragraph._element.getparent().remove(paragraph._element)
                return False
            full_text = full_text.replace(old, new)
            changed = True
            if old in keys_to_format or new in keys_to_format:
                needs_formatting = True

    if changed:
        # Очищаємо всі run'и
        for run in paragraph.runs:
            run.text = ''

        if needs_formatting:
            # Розділяємо текст на рядки
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if len(lines) > 1:
                # Очищаємо оригінальний параграф
                paragraph.runs[0].text = lines[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY  # Вирівнювання по ширині

                # Додаємо нові параграфи для решти рядків
                for line in lines[1:]:
                    new_paragraph = doc.add_paragraph(line)
                    new_paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            else:
                # Якщо лише один рядок
                paragraph.runs[0].text = full_text
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        else:
            # Якщо форматування не потрібне, просто оновлюємо текст
            paragraph.runs[0].text = full_text

    return True

def replace_text_in_cell(cell, doc):
    """Обробляє комірку, застосовуючи заміни та форматування."""
    for paragraph in cell.paragraphs:
        replace_text_in_paragraph(paragraph, doc)

def replace_text_in_docx(file_path):
    """Обробляє документ, застосовуючи заміни, видалення та форматування."""
    doc = Document(file_path)

    # Обробка параграфів
    paragraphs_to_remove = []
    for paragraph in doc.paragraphs:
        if not replace_text_in_paragraph(paragraph, doc):
            paragraphs_to_remove.append(paragraph)

    # Обробка таблиць
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_cell(cell, doc)

    doc.save(file_path)

for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        replace_text_in_docx(full_path)
        print(f'✅ Заміна, видалення та форматування виконано у файлі: {filename}')
