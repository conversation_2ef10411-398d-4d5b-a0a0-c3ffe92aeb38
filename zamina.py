import os
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt, RGBColor

# 🔷 Шлях до папки з Word-файлами та dyploma.docx
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"
dyploma_path = r"C:\Users\<USER>\Desktop\dyploma.docx"

# 🔄 Список замін
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна (основні) галузь (галузі) знань за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': '',
    'Name and status of institution (if different from 2.3)': '',
    'administering studies': '',
    'Зазначено у пункті 2.3': '',
    'Specified in 2.3': '',
    '2.5': '2.4',
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ': 'ІНФОРМАЦІЯ ПРО КВАЛІФІКАЦІЮ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках': 'Офіційна тривалість освітньо-професійної програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years': 'Official length of educational-professional programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ': 'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED': 'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності)': 'Найменування всіх закладів фахової передвищої освіти (структурних підрозділів або філій закладів фахової передвищої освіти), у яких здобувалася освітна кваліфікація (у тому числі заклади освіти, в яких здобувач фахової передвищої освіти вивчав окремі дисципліни за програмами академічної мобільності)',
    'Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of academic mobility': 'Names of all professional pre-higher education institutions (professional pre-higher education institutions separate structural units or branches) the qualification was gained in (including education institutions where the student of professional pre-higher education studied separate course units within the framework of academic mobility programme)',
    'Контактна інформація закладу вищої освіти (наукової установи)': 'Контактна інформація закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Contact information of the higher education (research) institution': 'Contact information of the professional pre-higher education institution (other educational entity)',
    'Керівник або уповноважена особа закладу вищої освіти': 'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Capacity': 'Head or other authorized person of professional pre-higher education institution',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)': 'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution': 'Position of the professional pre-higher education institution head or other authorized person (other educational entity)',
    'Печатка': 'Офіційна печатка',
    'Official stamp or seal': 'Official Seal',
}

# Список ключів, для яких потрібно додати абзац і вирівнювання по ширині
keys_to_format = [
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів фахової передвищої освіти (структурних підрозділів або філій закладів фахової передвищої освіти), у яких здобувалася освітня кваліфікація (у тому числі заклади освіти, в яких здобувач фахової передвищої освіти вивчав окремі дисципліни за програмами академічної мобільності)',
    'Names of all professional pre-higher education institutions (professional pre-higher education institutions separate structural units or branches) the qualification was gained in (including education institutions where the student of professional pre-higher education studied separate course units within the framework of academic mobility programme)',
    'Контактна інформація закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Contact information of the professional pre-higher education institution (other educational entity)',
    'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Head or other authorized person of professional pre-higher education institution',
    'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Position of the professional pre-higher education institution head or other authorized person (other educational entity)',
    'Офіційна печатка',
    'Official Seal',
]

def replace_text_in_paragraph(paragraph, doc):
    """Обробляє параграф: застосовує заміни, видаляє за потреби, додає абзац і вирівнювання."""
    full_text = ''.join(run.text for run in paragraph.runs).strip()
    changed = False
    needs_formatting = False

    # Перевірка на заміну або видалення
    for old, new in replacements.items():
        if old in full_text:
            if new == '':  # Якщо значення порожнє, видаляємо параграф
                paragraph._element.getparent().remove(paragraph._element)
                return False
            full_text = full_text.replace(old, new)
            changed = True
            if old in keys_to_format or new in keys_to_format:
                needs_formatting = True

    if changed:
        # Очищаємо всі run'и
        for run in paragraph.runs:
            run.text = ''

        if needs_formatting:
            # Розділяємо текст на рядки
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if len(lines) > 1:
                # Очищаємо оригінальний параграф
                paragraph.runs[0].text = lines[0]
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

                # Додаємо нові параграфи для решти рядків
                for line in lines[1:]:
                    new_paragraph = doc.add_paragraph(line)
                    new_paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            else:
                # Якщо лише один рядок
                paragraph.runs[0].text = full_text
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        else:
            # Якщо форматування не потрібне, просто оновлюємо текст
            paragraph.runs[0].text = full_text

    return True

def replace_text_in_cell(cell, doc):
    """Обробляє комірку, застосовуючи заміни та форматування."""
    for paragraph in cell.paragraphs:
        replace_text_in_paragraph(paragraph, doc)

def delete_after_section(doc):
    """Видаляє всі параграфи після заданого тексту."""
    start_index = None

    # Шукаємо текст "Офіційна печатка" або "Official Seal"
    for i, paragraph in enumerate(doc.paragraphs):
        full_text = ''.join(run.text for run in paragraph.runs).strip()
        if ("Офіційна печатка" in full_text or "Official Seal" in full_text) and "7.4" in full_text:
            start_index = i
            print(f"✅ Знайдено розділ на позиції {i+1}: '{full_text[:100]}...'")
            break

    if start_index is not None:
        paragraphs_to_delete = len(doc.paragraphs) - start_index - 1
        for i in reversed(range(start_index + 1, len(doc.paragraphs))):
            doc.paragraphs[i]._element.getparent().remove(doc.paragraphs[i]._element)
        print(f"🗑️ Видалено {paragraphs_to_delete} параграфів після розділу 7.4")
        return start_index
    else:
        print(f"⚠️ Не знайдено розділ з 'Офіційна печатка' або 'Official Seal'")
        # Показуємо доступні розділи для діагностики
        print("📋 Доступні розділи в документі:")
        for i, p in enumerate(doc.paragraphs):
            text = ''.join(run.text for run in p.runs).strip()
            if text and ("7." in text or "печатка" in text.lower() or "seal" in text.lower()):
                print(f"  {i+1}: {text[:100]}...")
        return None

def copy_dyploma_content(doc, dyploma_doc, insert_index):
    """Копіює вміст із dyploma.docx із збереженням форматування після заданого індексу."""
    if insert_index is None:
        insert_index = len(doc.paragraphs)

    for element in dyploma_doc.element.body:
        # Копіюємо параграфи
        if element.tag.endswith('p'):
            para = doc.add_paragraph()
            for run in element.xpath('.//w:r'):
                new_run = para.add_run()
                text = run.xpath('.//w:t/text()')
                new_run.text = text[0] if text else ""
                # Копіюємо форматування
                if run.xpath('.//w:b'):
                    new_run.bold = True
                if run.xpath('.//w:i'):
                    new_run.italic = True
                if run.xpath('.//w:u'):
                    new_run.underline = True
                font_size = run.xpath('.//w:sz/@w:val')
                if font_size:
                    new_run.font.size = Pt(int(font_size[0]) / 2)
                font_name = run.xpath('.//w:rFonts/@w:ascii')
                if font_name:
                    new_run.font.name = font_name[0]
                color = run.xpath('.//w:color/@w:val')
                if color:
                    try:
                        new_run.font.color.rgb = RGBColor.from_string(color[0])
                    except:
                        pass
            # Копіюємо вирівнювання та стиль параграфа
            align_val = element.xpath('.//w:jc/@w:val')
            para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY if align_val and align_val[0] == 'both' else None
            para.style = element.xpath('.//w:pStyle/@w:val')[0] if element.xpath('.//w:pStyle/@w:val') else 'Normal'

        # Копіюємо таблиці
        elif element.tag.endswith('tbl'):
            rows = len(element.xpath('.//w:tr'))
            cols = len(element.xpath('.//w:tr[1]/w:tc'))
            new_table = doc.add_table(rows=rows, cols=cols)
            # Використовуємо стандартний стиль таблиці або не встановлюємо стиль
            try:
                new_table.style = 'Table Grid'
            except:
                print("⚠️ Стиль 'Table Grid' недоступний, таблиця створена без стилю")
            for r, row in enumerate(element.xpath('.//w:tr')):
                for c, cell in enumerate(row.xpath('.//w:tc')):
                    new_cell = new_table.cell(r, c)
                    for p in cell.xpath('.//w:p'):
                        new_p = new_cell.add_paragraph()
                        for run in p.xpath('.//w:r'):
                            new_r = new_p.add_run()
                            text = run.xpath('.//w:t/text()')
                            new_r.text = ''.join(text) if text else ""
                            if run.xpath('.//w:b'):
                                new_r.bold = True
                            if run.xpath('.//w:i'):
                                new_r.italic = True
                            if run.xpath('.//w:u'):
                                new_r.underline = True
                            font_size = run.xpath('.//w:sz/@w:val')
                            if font_size:
                                new_r.font.size = Pt(int(font_size[0]) / 2)
                            font_name = run.xpath('.//w:rFonts/@w:ascii')
                            if font_name:
                                new_r.font.name = font_name[0]
                            color = run.xpath('.//w:color/@w:val')
                            if color:
                                try:
                                    new_r.font.color.rgb = RGBColor.from_string(color[0])
                                except:
                                    pass

    print("📥 Вставлено вміст з dyploma.docx")

def process_docx(file_path, dyploma_path):
    """Обробляє документ: застосовує заміни, видаляє текст після 7.4, вставляє вміст dyploma.docx."""
    try:
        doc = Document(file_path)
    except Exception as e:
        print(f"❌ Помилка при відкритті файлу {file_path}: {e}")
        return

    # Обробка параграфів
    for paragraph in doc.paragraphs:
        replace_text_in_paragraph(paragraph, doc)

    # Обробка таблиць
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                replace_text_in_cell(cell, doc)

    # Видалення тексту після "7.4 Офіційна печатка / 7.4 Official Seal"
    insert_index = delete_after_section(doc)

    # Вставка вмісту з dyploma.docx
    if os.path.exists(dyploma_path):
        try:
            dyploma_doc = Document(dyploma_path)
            copy_dyploma_content(doc, dyploma_doc, insert_index)
        except Exception as e:
            print(f"❌ Помилка при відкритті або обробці {dyploma_path}: {e}")
    else:
        print(f"❌ Не знайдено файл {dyploma_path}")

    # Збереження документа
    try:
        doc.save(file_path)
        print(f"✅ Оброблено: {os.path.basename(file_path)}\n")
    except Exception as e:
        print(f"❌ Помилка при збереженні {file_path}: {e}")

# Запуск обробки всіх файлів у папці
for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        process_docx(full_path, dyploma_path)
