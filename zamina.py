import os
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 🔷 Шлях до папки з Word-файлами та файлу dyploma.docx
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"
dyploma_path = r"C:\Users\<USER>\Desktop\dyploma.docx"
dyploma_path = r"C:\Users\<USER>\Desktop\dyploma.docx"
# Список замін (очищений від помилкових записів)
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна (основні) галузь (галузі) знань за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': '',
    'Name and status of institution (if different from 2.3)': '',
    'administering studies': '',
    'Зазначено у пункті 2.3': '',
    'Specified in 2.3': '',
    '2.5': '2.4',
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ': 'ІНФОРМАЦІЯ ПРО КВАЛІФІКАЦІЮ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках': 'Офіційна тривалість освітньо-професійної програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years': 'Official length of educational-professional programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ': 'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED': 'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності)': 'Найменування всіх закладів фахової передвищої освіти (структурних підрозділів або філій закладів фахової передвищої освіти), у яких здобувалася освітня кваліфікація (у тому числі заклади освіти, в яких здобувач фахової передвищої освіти вивчав окремі дисципліни за програмами академічної мобільності)',
    'Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of academic mobility)': 'Names of all professional pre-higher education institutions (professional pre-higher education institutions separate structural units or branches) the qualification was gained in (including education institutions where the student of professional pre-higher education studied separate course units within the framework of academic mobility programme)',
    'Контактна інформація закладу вищої освіти (наукової установи)': 'Контактна інформація закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Contact information of the higher education (research) institution': 'Contact information of the professional pre-higher education institution (other educational entity)',
    'Керівник або уповноважена особа закладу вищої освіти': 'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Capacity': 'Head or other authorized person of professional pre-higher education institution ',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)': 'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти (іншого суб’єкта освітньої діяльності)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution': 'Position of the professional pre-higher education institution head or other authorized person (other educational entity) ',
    'Печатка': 'Офіційна печатка',
    'Official stamp or seal': 'Official Seal',
}

# Список ключів, для яких потрібно додати абзац і вирівнювання по ширині
keys_to_format = [
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED',
    'Найменування всіх закладів вищої освіти (наукових установ) (відокремлених структурних підрозділів закладів вищої освіти), у яких здобувалася кваліфікація (у тому числі заклади освіти, в яких здобувач вищої освіти вивчав окремі дисципліни за програмами академічної мобільності) /Name of all higher education (research) institutions (separate structural units of higher education institutions) where the qualification has been gained (including education institutions where the holder of the qualification has been studying separate course units within the framework(s) of academic mobility)',
    'Контактна інформація закладу вищої освіти (наукової установи)',
    'Contact information of the higher education (research) institution',
    'Керівник або уповноважена особа закладу вищої освіти',
    'Capacity',
    'Посада керівника або іншої уповноваженої особи закладу вищої освіти (наукової установи)',
    'Position of the Head or another authorized person of the Higher Education (Research) Institution',
    'Печатка',
    'Official stamp or seal',
]


# --- Обробка параграфів ---
def replace_text_in_paragraph(paragraph):
    """Замінює текст в параграфі зі збереженням форматування"""
    original_text = paragraph.text
    modified = False

    for old, new in replacements.items():
        if old in original_text:
            # Зберігаємо оригінальне форматування
            original_alignment = paragraph.alignment
            original_style = paragraph.style

            # Замінюємо текст в кожному run'і окремо
            for run in paragraph.runs:
                if old in run.text:
                    # Зберігаємо форматування run'а
                    bold = run.bold
                    italic = run.italic
                    underline = run.underline
                    font_size = run.font.size
                    font_name = run.font.name
                    font_color = None
                    try:
                        font_color = run.font.color.rgb
                    except:
                        pass

                    # Замінюємо текст
                    run.text = run.text.replace(old, new)

                    # Відновлюємо форматування
                    run.bold = bold
                    run.italic = italic
                    run.underline = underline
                    if font_size:
                        run.font.size = font_size
                    if font_name:
                        run.font.name = font_name
                    if font_color:
                        try:
                            run.font.color.rgb = font_color
                        except:
                            pass

                    modified = True

            # Відновлюємо вирівнювання параграфа
            if modified:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                try:
                    paragraph.style = original_style
                except:
                    pass

    return modified

# --- Обробка таблиць ---
def replace_text_in_table(table):
    """Замінює текст в таблиці зі збереженням форматування"""
    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                replace_text_in_paragraph(paragraph)

# --- Видалити "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ..." і далі ---
def delete_national_education_section(doc):
    found = False
    for i, p in enumerate(doc.paragraphs):
        if "ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ" in p.text:
            found = True
            to_delete = doc.paragraphs[i:]
            break
    if found:
        for p in reversed(to_delete):
            p._element.getparent().remove(p._element)
        print("🗑️ Видалено розділ 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ...'")
    else:
        print("⚠️ Не знайдено розділ 'ІНФОРМАЦІЯ ПРО НАЦІОНАЛЬНУ СИСТЕМУ...'")



# --- Копіювати вміст з dyploma.docx ---
def copy_dyploma_content(doc, dyploma_doc):
    # Копіюємо параграфи зі збереженням форматування
    for para in dyploma_doc.paragraphs:
        new_p = doc.add_paragraph()
        new_p.alignment = para.alignment

        # Копіюємо стиль параграфа
        try:
            new_p.style = para.style
        except:
            pass

        # Копіюємо кожен run зі збереженням форматування
        for run in para.runs:
            new_run = new_p.add_run(run.text)

            # Копіюємо форматування run'а
            try:
                new_run.bold = run.bold
                new_run.italic = run.italic
                new_run.underline = run.underline
                if run.font.size:
                    new_run.font.size = run.font.size
                if run.font.name:
                    new_run.font.name = run.font.name
                if run.font.color.rgb:
                    new_run.font.color.rgb = run.font.color.rgb
            except:
                pass

    # Копіюємо таблиці зі збереженням форматування
    for table in dyploma_doc.tables:
        new_table = doc.add_table(rows=len(table.rows), cols=len(table.columns))

        # Копіюємо стиль таблиці
        try:
            new_table.style = table.style
        except:
            pass

        # Копіюємо вміст комірок зі збереженням форматування
        for r_idx, row in enumerate(table.rows):
            for c_idx, cell in enumerate(row.cells):
                new_cell = new_table.cell(r_idx, c_idx)

                # Очищаємо комірку
                new_cell.text = ""

                # Копіюємо кожен параграф в комірці
                for para_idx, para in enumerate(cell.paragraphs):
                    if para_idx == 0:
                        # Використовуємо перший існуючий параграф
                        new_para = new_cell.paragraphs[0]
                    else:
                        # Додаємо новий параграф
                        new_para = new_cell.add_paragraph()

                    new_para.alignment = para.alignment

                    # Копіюємо кожен run в параграфі
                    for run in para.runs:
                        new_run = new_para.add_run(run.text)

                        # Копіюємо форматування run'а
                        try:
                            new_run.bold = run.bold
                            new_run.italic = run.italic
                            new_run.underline = run.underline
                            if run.font.size:
                                new_run.font.size = run.font.size
                            if run.font.name:
                                new_run.font.name = run.font.name
                            if run.font.color.rgb:
                                new_run.font.color.rgb = run.font.color.rgb
                        except:
                            pass

    print("📥 Вміст з dyploma.docx вставлено зі збереженням форматування")

# --- Основна функція обробки файлу ---
def process_docx(path, dyploma_path):
    doc = Document(path)

    for p in doc.paragraphs:
        replace_text_in_paragraph(p)
    for t in doc.tables:
        replace_text_in_table(t)

    delete_national_education_section(doc)

    if os.path.exists(dyploma_path):
        dyploma_doc = Document(dyploma_path)
        copy_dyploma_content(doc, dyploma_doc)
    else:
        print(f"❌ Не знайдено файл {dyploma_path}")

    doc.save(path)
    print(f"✅ Оброблено: {os.path.basename(path)}\n")

# --- Запуск обробки всіх .docx ---
for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        process_docx(full_path, dyploma_path)
