import os
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 🔷 Шлях до папки з документами та dyploma.docx
folder_path = r"C:\Users\<USER>\Desktop\ЗВ-41"
dyploma_path = r"C:\Users\<USER>\Desktop\dyploma.docx"

# 🔁 Твої заміни
replacements = {
    'Назва кваліфікації та присвоєний ступінь': 'Назва освітньої кваліфікації та присвоєний освітньо-професійний ступінь (мовою оригіналу)',
    'Name of qualification and (if applicable) title conferred': 'Name of educational qualification and educational-professional degree conferred (in original language)',
    'Ступінь вищої освіти': 'Освітньо-професійний ступінь фахової передвищої освіти',
    'Degree': 'Professional pre-higher education educational-professional degree',
    'Професійна кваліфікація (у разі присвоєння)': 'Освітньо-професійна програма',
    'Professional Qualification (if awarded)': 'Educational-professional programme',
    'Фахівець з геодезії та землеустрою': '',
    'Specialist in geodesy and land management': '',
    'Основна (основні) галузь (галузі) знань за кваліфікацією': 'Професійна кваліфікація (у разі присвоєння)',
    'Main field(s) of study for the qualification': 'Professional qualification (if awarded)',
    '19 Архітектура та будівництво': 'Фахівець з геодезії та землеустрою',
    '19 Architecture and Construction': 'Specialist in geodesy and land management',
    'Найменування і статус закладу (якщо відмінні від п. 2.3), який реалізує освітню програму': '',
    'Name and status of institution (if different from 2.3)': '',
    'administering studies': '',
    'Зазначено у пункті 2.3': '',
    'Specified in 2.3': '',
    '2.5': '2.4',
    'ІНФОРМАЦІЯ ПРО РІВЕНЬ КВАЛІФІКАЦІЇ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ': 'ІНФОРМАЦІЯ ПРО КВАЛІФІКАЦІЮ І ТРИВАЛІСТЬ ЇЇ ЗДОБУТТЯ',
    'Тривалість освітньої програми в кредитах та/або роках': 'Офіційна тривалість освітньо-професійної програми в кредитах та/або роках',
    'Official duration of programme in credits and/or years': 'Official length of educational-professional programme in credits and/or years',
    'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЮ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ': 'ІНФОРМАЦІЯ ПРО ЗАВЕРШЕНУ ОСВІТНЬО-ПРОФЕСІЙНУ ПРОГРАМУ ТА ЗДОБУТІ РЕЗУЛЬТАТИ НАВЧАННЯ',
    'INFORMATION ON THE PROGRAMME COMPLETED AND THE RESULTS OBTAINED': 'INFORMATION ON THE COMPLETED EDUCATIONAL-PROFESSIONAL PROGRAMME AND LEARNING OUTCOMES',
    'Найменування всіх закладів вищої освіти': 'Найменування всіх закладів фахової передвищої освіти',
    'Name of all higher education': 'Names of all professional pre-higher education institutions',
    'Контактна інформація закладу вищої освіти': 'Контактна інформація закладу фахової передвищої освіти',
    'Contact information of the higher education': 'Contact information of the professional pre-higher education institution',
    'Керівник або уповноважена особа закладу вищої освіти': 'Керівник або уповноважена особа закладу фахової передвищої освіти',
    'Capacity': 'Head or other authorized person of professional pre-higher education institution',
    'Посада керівника або іншої уповноваженої особи': 'Посада керівника або іншої уповноваженої особи закладу фахової передвищої освіти',
    'Position of the Head or another authorized person': 'Position of the professional pre-higher education institution head or other authorized person',
    'Печатка': 'Офіційна печатка',
    'Official stamp or seal': 'Official Seal',
}

# --- Заміна в параграфах ---
def replace_text_in_paragraph(paragraph):
    for old, new in replacements.items():
        if old in paragraph.text:
            paragraph.text = paragraph.text.replace(old, new)
            paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

# --- Заміна в таблицях ---
def replace_text_in_table(table):
    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                replace_text_in_paragraph(paragraph)

# --- Видалення розділу з певного місця ---
def delete_paragraphs_from(doc, start_text):
    for i, p in enumerate(doc.paragraphs):
        if start_text in p.text:
            for j in reversed(range(i, len(doc.paragraphs))):
                p = doc.paragraphs[j]
                p._element.getparent().remove(p._element)
            print("🗑️ Видалено розділ:", start_text)
            return
    print("⚠️ Не знайдено розділ:", start_text)

# --- Копіювання вмісту з dyploma.docx ---
def copy_dyploma_content(doc, dyploma_doc):
    for para in dyploma_doc.paragraphs:
        new_p = doc.add_paragraph()
        new_p.alignment = para.alignment
        new_p.style = para.style
        for run in para.runs:
            new_run = new_p.add_run(run.text)
            new_run.bold = run.bold
            new_run.italic = run.italic
            new_run.underline = run.underline
            new_run.font.size = run.font.size
            new_run.font.name = run.font.name
            try:
                new_run.font.color.rgb = run.font.color.rgb
            except:
                pass

    for table in dyploma_doc.tables:
        new_table = doc.add_table(rows=len(table.rows), cols=len(table.columns))
        new_table.style = table.style
        for r in range(len(table.rows)):
            for c in range(len(table.columns)):
                cell = new_table.cell(r, c)
                old_cell = table.cell(r, c)
                cell.text = ""
                for para in old_cell.paragraphs:
                    new_p = cell.add_paragraph()
                    for run in para.runs:
                        new_r = new_p.add_run(run.text)
                        new_r.bold = run.bold
                        new_r.italic = run.italic
                        new_r.underline = run.underline
                        new_r.font.size = run.font.size
                        new_r.font.name = run.font.name
                        try:
                            new_r.font.color.rgb = run.font.color.rgb
                        except:
                            pass
    print("📥 Вставлено вміст з dyploma.docx")

# --- Основна функція ---
def process_docx(path, dyploma_path):
    doc = Document(path)

    for p in doc.paragraphs:
        replace_text_in_paragraph(p)
    for t in doc.tables:
        replace_text_in_table(t)

    delete_paragraphs_from(doc, "ІНФОРМАЦІЯ ПРО СИСТЕМУ ФАХОВОЇ ПЕРЕДВИЩОЇ ОСВІТИ")

    if os.path.exists(dyploma_path):
        dyploma_doc = Document(dyploma_path)
        copy_dyploma_content(doc, dyploma_doc)
    else:
        print(f"❌ Не знайдено файл {dyploma_path}")

    doc.save(path)
    print(f"✅ Оброблено: {os.path.basename(path)}\n")

# --- Запуск ---
for filename in os.listdir(folder_path):
    if filename.endswith('.docx') and not filename.startswith('~$'):
        full_path = os.path.join(folder_path, filename)
        process_docx(full_path, dyploma_path)
